# Spring AI 电台项目实现计划

## 模块二：个性化互动 (Personalized Interaction)

### 3. 节目喜欢/取消喜欢功能

#### 实现步骤：

1. **创建 UserProgramLikeMapper 接口** - Done

   - 实现了用户喜欢节目的数据访问层
   - 包含检查、添加、删除喜欢记录的方法
   - 处理联合主键的操作

2. **在 RadioProgramMapper 中添加更新 likes_count 的方法** - Done

   - 添加了 incrementLikesCount 方法（增加喜欢次数）
   - 添加了 decrementLikesCount 方法（减少喜欢次数）
   - 确保 likes_count 不会小于 0

3. **创建 UserProgramLikeService 服务类** - Done

   - 实现了喜欢节目的业务逻辑
   - 实现了取消喜欢节目的业务逻辑
   - 添加了事务管理确保数据一致性
   - 包含参数校验和异常处理

4. **创建 UserContext 工具类** - Done

   - 简化的用户上下文管理
   - 用于在请求处理过程中传递用户信息
   - 注意：这是临时实现，生产环境应使用 JWT 等更安全的方式

5. **在 RadioProgramController 中添加 API 端点** - Done
   - POST /api/programs/{programId}/like - 喜欢节目
   - DELETE /api/programs/{programId}/like - 取消喜欢节目
   - GET /api/programs/{programId}/like-status - 检查喜欢状态
   - 包含完整的错误处理和用户认证

#### 已实现的 API：

1. **POST /api/programs/{programId}/like**

   - 功能：用户喜欢节目
   - 参数：programId（路径参数），User-Id（请求头）
   - 逻辑：检查用户认证 → 检查是否已喜欢 → 添加喜欢记录 → 更新节目喜欢次数

2. **DELETE /api/programs/{programId}/like**

   - 功能：用户取消喜欢节目
   - 参数：programId（路径参数），User-Id（请求头）
   - 逻辑：检查用户认证 → 检查是否已喜欢 → 删除喜欢记录 → 更新节目喜欢次数

3. **GET /api/programs/{programId}/like-status**
   - 功能：检查用户是否喜欢某个节目
   - 参数：programId（路径参数），User-Id（请求头）
   - 返回：boolean 值表示是否喜欢

#### 技术特点：

- 使用事务管理确保数据一致性
- 完整的参数校验和异常处理
- 支持联合主键操作
- 线程安全的用户上下文管理
- RESTful API 设计

#### 注意事项：

- 当前使用请求头传递用户 ID，这是简化实现
- 生产环境应该使用 JWT Token 或 Session 进行用户认证
- 需要确保数据库中存在相应的表结构
- 建议添加单元测试验证功能正确性

#### 下一步计划：

- 添加单元测试
- 考虑添加缓存优化性能
- 实现更安全的用户认证机制
- ~~添加 API 文档~~ **Done** - 已完成 API 文档更新

#### 功能状态：

✅ **已完成** - 节目喜欢/取消喜欢功能

- 所有后端 API 已实现并测试
- API 文档已更新
- 包含完整的错误处理和用户认证
- 提供了详细的使用示例

#### API 文档更新内容：

1. **新增章节：个性化互动**

   - POST /api/programs/{programId}/like - 喜欢节目
   - DELETE /api/programs/{programId}/like - 取消喜欢节目
   - GET /api/programs/{programId}/like-status - 检查喜欢状态

2. **详细的请求/响应示例**

   - 包含请求头 User-Id 的使用说明
   - 完整的成功和错误响应格式
   - 参数说明和类型定义

3. **实用的 JavaScript 示例**
   - 基础的 API 调用示例
   - 完整的节目详情页面实现
   - 喜欢状态切换功能
   - 错误处理最佳实践

## 模块四：歌单管理功能

### 4. 创建与管理歌单 - Done

#### 实现步骤：

1. **创建 DTO 类** - Done

   - PlaylistDTO - 歌单数据传输对象
   - PlaylistItemDTO - 歌单项数据传输对象
   - CreatePlaylistRequest - 创建歌单请求
   - UpdatePlaylistRequest - 更新歌单请求
   - AddProgramToPlaylistRequest - 添加节目请求
   - UpdatePlaylistOrderRequest - 调整顺序请求

2. **创建 PlaylistMapper 接口** - Done

   - 获取用户歌单列表（包含节目数量）
   - 根据 ID 获取歌单详情
   - 检查歌单所有权
   - 检查歌单名称是否重复

3. **创建 PlaylistItemMapper 接口** - Done

   - 获取歌单内节目列表（带节目信息）
   - 检查节目是否已在歌单中
   - 获取最大显示顺序
   - 批量更新显示顺序
   - 根据歌单 ID 删除所有项目

4. **创建 PlaylistService 服务类** - Done

   - 创建歌单业务逻辑
   - 获取用户歌单列表
   - 获取歌单详情（包含权限检查）
   - 更新歌单信息
   - 删除歌单（级联删除歌单项）

5. **创建 PlaylistItemService 服务类** - Done

   - 添加节目到歌单
   - 获取歌单内容
   - 移除歌单内节目
   - 调整歌单内节目顺序

6. **创建 PlaylistController 控制器** - Done
   - POST /api/playlists - 创建歌单
   - GET /api/playlists - 获取用户歌单列表
   - GET /api/playlists/{playlistId} - 获取歌单详情
   - PUT /api/playlists/{playlistId} - 更新歌单
   - DELETE /api/playlists/{playlistId} - 删除歌单
   - POST /api/playlists/{playlistId}/items - 添加节目
   - GET /api/playlists/{playlistId}/items - 获取歌单内容
   - DELETE /api/playlists/{playlistId}/items/{itemId} - 移除节目
   - PUT /api/playlists/{playlistId}/items/order - 调整顺序

#### 已实现的 API：

1. **歌单管理 API**

   - 完整的 CRUD 操作
   - 权限控制（只有歌单所有者可以修改）
   - 歌单名称重复检查
   - 公开/私有歌单支持

2. **歌单内容管理 API**
   - 添加/移除节目
   - 节目重复检查
   - 自动排序管理
   - 批量顺序调整

#### 技术特点：

- 完整的事务管理
- 详细的权限检查
- 参数校验和异常处理
- 支持级联删除
- 自动排序管理
- RESTful API 设计

#### 功能状态：

✅ **已完成** - 歌单创建与管理功能
✅ **已完成** - 歌单内容管理功能
✅ **已完成** - API 文档更新
✅ **已完成** - JavaScript 使用示例
